# Fiscal Document Service

## Local Development

### Requirements

- Java 21 JDK (for running the app locally)
- Gradle 8.5+
- Docker (for dependencies)

### Private Dependency Setup

To download private Cloudbeds dependencies, you need to add your GitHub username and a personal access token with `repo` and `read:packages` scopes to the `gradle.properties` file.

### Initial setup

1. Create .env file by running:
   ```bash
   make setup-env
   ```

### Running the Java Service Locally

1. Build the project:
   ```bash
   ./gradlew build
   ```
2. Export environment variables:
   ```bash
   export $(grep -v '^#' .env | xargs)
   ```
3. Build and start docker containers:
   ```bash
   make docker-build
   ```
4. Run the service:
   ```bash
   make start
   ```

### Accessing Swagger UI

Once the service is running, open:
```
http://localhost:8700/swagger-ui/index.html
```

### Development Notes

- The Java app is run on the host for local development; dependencies are managed with Docker Compose.
- The provided Dockerfile is for building production images, not for hot-reload or live development.
- **VPN must be enabled** to access the accounting service when retrieving transaction information for invoices.
- A [Postman collection](https://www.postman.co/workspace/Personal-Workspace~8828300d-0c39-4ad0-941e-bbd243575ced/collection/********-a35e4444-0054-4255-9108-eb40cd6f26f4?action=share&creator=********&active-environment=********-38188b51-009a-4175-902b-d1902019e9e4)
  is available for testing API endpoints.

### Docker
To access AWS ECR Docker images:

- Configure aws: `~/.aws/config`
```ini
[profile Prod]
sso_start_url = https://cloudbeds-aws-sso.awsapps.com/start
sso_region = us-west-2
sso_account_id = ************
sso_role_name = Accounting-************
region = us-west-2
output = json
```

- Run the following command to login to AWS ECR:
```bash
aws sso login --profile Prod
aws --profile=Prod ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin ************.dkr.ecr.us-west-2.amazonaws.com
```

### Development

1. gRPC server is available at [localhost:${GRPC_PORT}](http://localhost:9090)
1. Local PostgreSQL is available at [localhost:${DB_PORT}](http://localhost:5433)

### Debugging

For debugging instructions visit <https://cloudbeds.atlassian.net/wiki/spaces/MC/pages/**********/Debug+instructions>

### LocalStack
```bash
docker run --rm -it -p 4566:4566 -p 4510-4559:4510-4559 localstack/localstack
```

To see files in S3 open http://localhost:4566/fiscal-documents

### SonarQube report

To start SonarQube service run :

```bash
make analyze
```

Check [localhost:9000/projects](http://localhost:9000/projects?sort=-analysis_date) for SonarQube report

### OpenAPI definition

Check [localhost:8700/swagger-ui](http://localhost:8700/swagger-ui/index.html) for OpenAPI definition
