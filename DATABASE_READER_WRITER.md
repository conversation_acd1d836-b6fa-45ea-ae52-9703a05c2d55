# Database Reader/Writer Pattern Implementation

## 🎯 Overview

This implementation provides automatic database routing for read and write operations in the fiscal-document-service. It uses Spring's `AbstractRoutingDataSource` to route queries to different database instances based on transaction type.

## 🏗️ Architecture

### Core Components

1. **`DbType`** - Enum defining READ/WRITE database types
2. **`DbContextHolder`** - ThreadLocal storage for current database context
3. **`ClientDataSourceRouter`** - Routes queries to appropriate database
4. **`ReadOnlyConnectionInterceptor`** - AOP aspect for automatic routing
5. **`DatabaseConfig`** - Spring configuration for datasource routing

### How It Works

```
@Transactional(readOnly=true) → READ database  (DB_HOST_READER)
@Transactional              → WRITE database (DB_HOST)
```

## 🚀 Quick Start

### 1. Enable the Feature

```bash
# In .env file
DB_READER_WRITER_ENABLED=true
DB_HOST_READER=your-read-replica-host
```

### 2. Configure Databases

```yaml
# application.yml already configured with:
db:
  writer:
    url: "jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
  reader:
    url: "jdbc:postgresql://${DB_HOST_READER}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
```

### 3. Use in Code

```java
@Service
public class MyService {
    
    @Transactional(readOnly = true)
    public List<Entity> findAll() {
        // This will use the READ database
        return repository.findAll();
    }
    
    @Transactional
    public Entity save(Entity entity) {
        // This will use the WRITE database
        return repository.save(entity);
    }
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_READER_WRITER_ENABLED` | Enable/disable reader/writer pattern | `false` |
| `DB_HOST` | Writer database host | `localhost` |
| `DB_HOST_READER` | Reader database host | `localhost` |
| `DB_USER` | Database username | `user` |
| `DB_PASSWORD` | Database password | `pass` |
| `DB_PORT` | Database port | `5432` |
| `DB_NAME` | Database name | `fiscaldocument` |

### Feature Flag

The reader/writer pattern is controlled by:
```yaml
application:
  database:
    reader-writer:
      enabled: ${DB_READER_WRITER_ENABLED:false}
```

When disabled, the application uses Spring Boot's default single datasource configuration.

## 🧪 Testing

### Run Tests

```bash
# Test the implementation
./test-db-routing.sh

# Run specific unit tests
./gradlew test --tests "DbContextHolderTest" --tests "ReadOnlyConnectionInterceptorTest"
```

### Manual Testing

1. **Enable the feature**: Set `DB_READER_WRITER_ENABLED=true`
2. **Configure different hosts**: Set `DB_HOST_READER` to a different host
3. **Monitor logs**: Check which database connections are being used
4. **Verify routing**: Use database monitoring to confirm read/write separation

## 📊 Monitoring

### Database Connection Monitoring

Monitor your database connections to verify:
- Read queries go to `DB_HOST_READER`
- Write queries go to `DB_HOST`
- Connection pooling works correctly

### Logging

Enable SQL logging to see which database is being used:
```yaml
spring:
  jpa:
    show-sql: true
    properties:
      hibernate:
        format-sql: true
```

## 🔒 Best Practices

### 1. Transaction Boundaries
```java
// ✅ Good - Clear read-only transaction
@Transactional(readOnly = true)
public List<User> getActiveUsers() {
    return userRepository.findByActive(true);
}

// ✅ Good - Clear write transaction
@Transactional
public User createUser(User user) {
    return userRepository.save(user);
}
```

### 2. Avoid Mixed Operations
```java
// ❌ Bad - Mixed read/write in same method
@Transactional(readOnly = true)
public void processUsers() {
    List<User> users = userRepository.findAll(); // READ
    users.forEach(user -> {
        user.setLastAccessed(new Date());
        userRepository.save(user); // WRITE - Will fail!
    });
}

// ✅ Good - Separate read and write operations
@Transactional(readOnly = true)
public List<User> getUsers() {
    return userRepository.findAll();
}

@Transactional
public void updateUsers(List<User> users) {
    userRepository.saveAll(users);
}
```

### 3. Connection Pool Configuration
```yaml
db:
  writer:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
  reader:
    hikari:
      maximum-pool-size: 30  # More connections for read replica
      minimum-idle: 10
```

## 🚨 Troubleshooting

### Common Issues

1. **Spring Context Fails to Start**
   - Check that `DB_HOST_READER` is accessible
   - Verify database credentials
   - Ensure feature flag is correctly set

2. **Queries Going to Wrong Database**
   - Verify `@Transactional` annotations
   - Check transaction propagation
   - Ensure AOP is enabled

3. **Connection Pool Exhaustion**
   - Monitor connection usage
   - Adjust pool sizes
   - Check for connection leaks

### Debug Mode

Enable debug logging:
```yaml
logging:
  level:
    com.cloudbeds.fiscaldocument.support.database: DEBUG
    org.springframework.jdbc: DEBUG
```

## 🔄 Migration Strategy

### Phase 1: Deploy with Feature Disabled
```bash
DB_READER_WRITER_ENABLED=false
```

### Phase 2: Enable with Same Database
```bash
DB_READER_WRITER_ENABLED=true
DB_HOST_READER=same-as-DB_HOST  # Test routing with same DB
```

### Phase 3: Enable with Read Replica
```bash
DB_READER_WRITER_ENABLED=true
DB_HOST_READER=your-read-replica-host
```

## 📈 Performance Benefits

- **Read Scaling**: Distribute read load across multiple replicas
- **Write Performance**: Dedicated write database for better write performance
- **High Availability**: Fallback to writer if reader fails
- **Geographic Distribution**: Place read replicas closer to users

## 🔗 Related Documentation

- [Spring AbstractRoutingDataSource](https://docs.spring.io/spring-framework/docs/current/javadoc-api/org/springframework/jdbc/datasource/lookup/AbstractRoutingDataSource.html)
- [Spring Transaction Management](https://docs.spring.io/spring-framework/docs/current/reference/html/data-access.html#transaction)
- [HikariCP Configuration](https://github.com/brettwooldridge/HikariCP#configuration-knobs-baby)
