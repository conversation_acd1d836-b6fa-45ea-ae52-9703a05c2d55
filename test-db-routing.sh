#!/bin/bash

echo "🔧 Database Reader/Writer Pattern Test Script"
echo "=============================================="

# Set JAVA_HOME
export JAVA_HOME=/usr/local/Cellar/openjdk@21/21.0.7/libexec/openjdk.jdk/Contents/Home

echo "📋 Step 1: Test compilation without reader/writer (default mode)"
echo "No DB_HOST_READER configured - uses single datasource"
./gradlew compileJava --no-daemon --quiet
if [ $? -eq 0 ]; then
    echo "✅ Compilation successful in default mode"
else
    echo "❌ Compilation failed in default mode"
    exit 1
fi

echo ""
echo "📋 Step 2: Test unit tests for database routing classes"
./gradlew test --tests "DbContextHolderTest" --tests "ReadOnlyConnectionInterceptorTest" --no-daemon --quiet
if [ $? -eq 0 ]; then
    echo "✅ Unit tests passed"
else
    echo "❌ Unit tests failed"
fi

echo ""
echo "📋 Step 3: Test with reader/writer pattern enabled"
echo "DB_HOST_READER is configured - enables reader/writer routing"
./gradlew compileJava --no-daemon --quiet
if [ $? -eq 0 ]; then
    echo "✅ Compilation successful with reader/writer enabled"
else
    echo "❌ Compilation failed with reader/writer enabled"
    exit 1
fi

echo ""
echo "🎉 All tests completed successfully!"
echo ""
echo "📖 Usage Instructions:"
echo "1. Configure DB_HOST_READER to point to your read replica"
echo "2. Reader/writer pattern automatically enabled when DB_HOST_READER is set"
echo "3. Use @Transactional(readOnly=true) for read operations"
echo "4. Use @Transactional for write operations"
