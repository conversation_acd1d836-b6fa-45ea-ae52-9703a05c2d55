#!/bin/bash

echo "🔧 Database Reader/Writer Pattern Test Script"
echo "=============================================="

# Set JAVA_HOME
export JAVA_HOME=/usr/local/Cellar/openjdk@21/21.0.7/libexec/openjdk.jdk/Contents/Home

echo "📋 Step 1: Test compilation without reader/writer (default mode)"
echo "DB_READER_WRITER_ENABLED=false"
./gradlew compileJava --no-daemon --quiet
if [ $? -eq 0 ]; then
    echo "✅ Compilation successful in default mode"
else
    echo "❌ Compilation failed in default mode"
    exit 1
fi

echo ""
echo "📋 Step 2: Test unit tests for database routing classes"
./gradlew test --tests "DbContextHolderTest" --tests "ReadOnlyConnectionInterceptorTest" --no-daemon --quiet
if [ $? -eq 0 ]; then
    echo "✅ Unit tests passed"
else
    echo "❌ Unit tests failed"
fi

echo ""
echo "📋 Step 3: Enable reader/writer pattern"
echo "Setting DB_READER_WRITER_ENABLED=true in .env"
sed -i '' 's/DB_READER_WRITER_ENABLED=false/DB_READER_WRITER_ENABLED=true/' .env

echo ""
echo "📋 Step 4: Test compilation with reader/writer enabled"
./gradlew compileJava --no-daemon --quiet
if [ $? -eq 0 ]; then
    echo "✅ Compilation successful with reader/writer enabled"
else
    echo "❌ Compilation failed with reader/writer enabled"
    # Restore original setting
    sed -i '' 's/DB_READER_WRITER_ENABLED=true/DB_READER_WRITER_ENABLED=false/' .env
    exit 1
fi

echo ""
echo "📋 Step 5: Restore default setting"
sed -i '' 's/DB_READER_WRITER_ENABLED=true/DB_READER_WRITER_ENABLED=false/' .env
echo "✅ Restored DB_READER_WRITER_ENABLED=false"

echo ""
echo "🎉 All tests completed successfully!"
echo ""
echo "📖 Usage Instructions:"
echo "1. Set DB_READER_WRITER_ENABLED=true in .env to enable reader/writer pattern"
echo "2. Configure DB_HOST_READER to point to your read replica"
echo "3. Use @Transactional(readOnly=true) for read operations"
echo "4. Use @Transactional for write operations"
