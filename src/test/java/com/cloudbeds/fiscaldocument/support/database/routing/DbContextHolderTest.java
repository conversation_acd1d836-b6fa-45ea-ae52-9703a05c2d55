package com.cloudbeds.fiscaldocument.support.database.routing;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class DbContextHolderTest {

    @AfterEach
    void tearDown() {
        DbContextHolder.clearDbType();
    }

    @Test
    void shouldSetAndGetDbType() {
        DbContextHolder.setDbType(DbType.READ);
        assertThat(DbContextHolder.getDbType()).isEqualTo(DbType.READ);

        DbContextHolder.setDbType(DbType.WRITE);
        assertThat(DbContextHolder.getDbType()).isEqualTo(DbType.WRITE);
    }

    @Test
    void shouldClearDbType() {
        DbContextHolder.setDbType(DbType.READ);
        DbContextHolder.clearDbType();
        assertThat(DbContextHolder.getDbType()).isNull();
    }

    @Test
    void shouldThrowExceptionForNullDbType() {
        assertThatThrownBy(() -> DbContextHolder.setDbType(null))
            .isInstanceOf(NullPointerException.class);
    }

    @Test
    void shouldReturnNullWhenNotSet() {
        assertThat(DbContextHolder.getDbType()).isNull();
    }
}
