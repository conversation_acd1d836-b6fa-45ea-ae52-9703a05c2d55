package com.cloudbeds.fiscaldocument.support.database.aspect;

import com.cloudbeds.fiscaldocument.support.database.routing.DbContextHolder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReadOnlyConnectionInterceptorTest {

    @Mock
    private ProceedingJoinPoint proceedingJoinPoint;

    @Mock
    private Transactional transactional;

    private ReadOnlyConnectionInterceptor interceptor;

    @BeforeEach
    void setUp() {
        interceptor = new ReadOnlyConnectionInterceptor();
    }

    @AfterEach
    void tearDown() {
        DbContextHolder.clearDbType();
    }

    @Test
    void shouldSetReadDbTypeForReadOnlyTransaction() throws Throwable {
        when(transactional.readOnly()).thenReturn(true);
        when(proceedingJoinPoint.proceed()).thenReturn("result");

        Object result = interceptor.proceed(proceedingJoinPoint, transactional);

        assertThat(result).isEqualTo("result");
        assertThat(DbContextHolder.getDbType()).isNull(); // Should be cleared after execution
    }

    @Test
    void shouldSetWriteDbTypeForWriteTransaction() throws Throwable {
        when(transactional.readOnly()).thenReturn(false);
        when(proceedingJoinPoint.proceed()).thenReturn("result");

        Object result = interceptor.proceed(proceedingJoinPoint, transactional);

        assertThat(result).isEqualTo("result");
        assertThat(DbContextHolder.getDbType()).isNull(); // Should be cleared after execution
    }

    @Test
    void shouldClearDbTypeEvenWhenExceptionThrown() throws Throwable {
        when(transactional.readOnly()).thenReturn(true);
        when(proceedingJoinPoint.proceed()).thenThrow(new RuntimeException("Test exception"));

        try {
            interceptor.proceed(proceedingJoinPoint, transactional);
        } catch (RuntimeException e) {
            // Expected
        }

        assertThat(DbContextHolder.getDbType()).isNull(); // Should be cleared even after exception
    }

    @Test
    void shouldHaveCorrectOrder() {
        assertThat(interceptor.getOrder()).isEqualTo(Integer.MIN_VALUE + 1);
    }
}
