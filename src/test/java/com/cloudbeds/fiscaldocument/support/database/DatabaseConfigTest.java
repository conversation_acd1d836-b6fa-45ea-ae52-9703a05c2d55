package com.cloudbeds.fiscaldocument.support.database;

import javax.sql.DataSource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
@ActiveProfiles("test")
class DatabaseConfigTest {

    @Autowired
    private DatabaseConfig databaseConfig;

    @Test
    void shouldCreateWriterDataSource() {
        DataSource writerDataSource = databaseConfig.readWriteConfiguration();
        assertThat(writerDataSource).isNotNull();
    }

    @Test
    void shouldCreateReaderDataSource() {
        DataSource readerDataSource = databaseConfig.readOnlyConfiguration();
        assertThat(readerDataSource).isNotNull();
    }

    @Test
    void shouldCreateClientDataSource() {
        DataSource clientDataSource = databaseConfig.clientDatasource();
        assertThat(clientDataSource).isNotNull();
    }
}
