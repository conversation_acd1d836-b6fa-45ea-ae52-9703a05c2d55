debug: false

server:
  port: ${APP_PORT:8700}
  error:
    whitelabel:
      enabled: false
  shutdown: graceful


spring:
  application:
    name: fiscal-document-service
  main:
    banner-mode: off

  liquibase:
    enabled: ${LIQUIBASE_ENABLED:false}
    change-log: classpath:db/changelog/databaseChangeLog.xml
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?stringtype=unspecified
    password: ${DB_PASSWORD}
    user: ${DB_USER}
    default-schema: ${DB_SCHEME:public}

  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?stringtype=unspecified
    password: ${DB_PASSWORD}
    username: ${DB_USER}
    hikari:
      minimum-idle: ${HIKARI_MINIMUM_IDLE:4}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      data-source-properties:
        socketTimeout: 30000 # 30 seconds
      schema: ${DB_SCHEME:public}

  kafka:
    bootstrap-servers: ${KAFKA_SERVER}
    consumer:
      key-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      properties:
        specific.avro.reader: true
        auto.offset.reset: earliest
        max.poll.interval.ms: ${KAFKA_MAX_POLL_INTERVAL:6000000}
    producer:
      key-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      properties:
        avro.remove.java.properties: true
        acks: all
        specific.avro.writer: true
    properties:
      schema.registry.url: ${CONFLUENT_SCHEMA_REGISTRY_URL}
      sasl.mechanism: PLAIN
      ssl.endpoint.identification.algorithm: https
      sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${CONFLUENT_API_KEY}" password="${CONFLUENT_API_SECRET}";
      security.protocol: SASL_SSL
      client.dns.lookup: use_all_dns_ips
      basic.auth.credentials.source: USER_INFO
      basic.auth.user.info: ${CONFLUENT_SCHEMA_KEY}:${CONFLUENT_SCHEMA_SECRET}

  jpa:
    open-in-view: false
    show-sql: ${JPA_SHOW_SQL:false}
    # Allows Hibernate to generate SQL optimized for a particular DBMS
    properties:
      hibernate:
        format-sql: true
        jdbc:
          time_zone: UTC
          batch_size: ${HIBERNATE_JDBC_BATCH_SIZE:50}
        default_schema: ${DB_SCHEME:public}
    # Hibernate ddl auto (create, create-drop, update): with "update" the database
    # schema will be automatically updated accordingly to java entities found in
    # the project
    hibernate:
      ddl-auto: validate

  data:
    redis:
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      ssl:
        enabled: ${REDIS_SSL_ENABLED:true}
      timeout: 2000ms
      connect-timeout: 2000ms

cloudbeds-shared:
  default-authentication-config:
    enabled: false
  authentication-filter:
    enabled: true
  security-expressions:
    enabled: true

grpc:
  server:
    port: ${GRPC_PORT:9090}
    reflection-service-enabled: true
  services:
    accounting:
      host: ${ACCOUNTING_SERVICE_HOST}
      port: ${ACCOUNTING_SERVICE_PORT}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS}
    organization:
      host: ${ORGANIZATION_SERVICE_HOST}
      port: ${ORGANIZATION_SERVICE_PORT}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS}
    group-profile:
      host: ${MFD_GRPC_SERVICE_HOST}
      port: ${MFD_GRPC_SERVICE_PORT}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS}
    booking:
      host: ${MFD_GRPC_SERVICE_HOST}
      port: ${MFD_GRPC_SERVICE_PORT}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS}
    guest-requirements:
      host: ${GUEST_REQUIREMENT_SERVICE_HOST}
      port: ${GUEST_REQUIREMENT_SERVICE_PORT}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS}
    guest:
      host: ${GUEST_SERVICE_HOST}
      port: ${GUEST_SERVICE_PORT}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS}
    marketplace:
      host: ${MFD_GRPC_SERVICE_HOST}
      port: ${MFD_GRPC_SERVICE_PORT}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS}

cloudbeds:
  mappings-service-url: ${MAPPING_SERVICE_URL:https://api.cloudbeds-stage.com/mapping/}

distributedid:
  enabled: true
  pool-size: 1000
  threshold-percent: 0.7
  server-host: ${DISTRIBUTED_ID_SERVICE_HOST:127.0.0.1}
  server-port: ${DISTRIBUTED_ID_SERVICE_PORT:19090}
  service-id: ${spring.application.name}
  channel-type: ${DISTRIBUTED_ID_CHANNEL_TYPE:PLAINTEXT}
  lazy-init: true

# ===============================
# = Spring Doc - Swagger
# ===============================
springdoc:
  swagger-ui:
    enabled: ${SWAGGER_UI:false}
    url: /fiscal-document/openapi.yaml
    path: /fiscal-document/swagger-ui
  api-docs:
    path: /fiscal-document/v3/api-docs
    groups:
      enable: ${SWAGGER_UI:false}

logging:
  level:
    root: info
    org:
      springframework:
        web: INFO
        grpc: INFO

management:
  health:
    defaults:
      enabled: false
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health, metrics, prometheus
  endpoint:
    health:
      probes:
        enabled: true
      show-details: ALWAYS
  server:
    port: ${ACTUATOR_PORT:9001}
  metrics:
    tags:
      env: ${DD_ENV}
      tier: ${DD_TIER}
    data:
      repository:
        autotime:
          enabled: false
  datadog:
    metrics:
      export:
        enabled: ${DATADOG_METRICS_ENABLED:false}
        api-key: ${DATADOG_API_KEY}
        application-key: ${DATADOG_APPLICATION_KEY}
        step: 10s
        host-tag: ${DATADOG_HOST_KEY}


application:
  aws:
    s3:
      bucket-name: ${S3_BUCKET_NAME}
      mfd-bucket-name: ${S3_MFD_BUCKET_NAME:}
    region: ${AWS_REGION}
    endpoint: ${AWS_ENDPOINT:}
  feature-management:
    provider-type: ${FEATURE_MANAGEMENT_PROVIDER:LAUNCH_DARKLY}
    launch-darkly:
      sdk-key: ${LAUNCH_DARKLY_SDK_KEY:}

sendgrid:
  api_key: ${SENDGRID_API_KEY}
  sender: ${SENDGRID_SENDER}

config:
  island: ${ISLAND}

topics:
  invoice_setup: ${config.island}.cdc.mfd.acessa.invoice_setup
  invoices: ${config.island}.cdc.mfd.acessa.a_htl_invoices
  fiscal_document_events: ${config.island}.service.fiscal-document.fiscal_document_events
  fiscal_documents: ${config.island}.service.fiscal-document.fiscal_documents

consumer:
  invoices:
    group_id: ${CONSUMER_GROUP_INVOICES}
    enabled: ${INVOICES_CONSUMER:false}
  invoice_setup:
    group_id: ${CONSUMER_GROUP_INVOICE_SETUP}
    enabled: ${INVOICE_SETUP_CONSUMER:false}
  fiscal_document_events:
    group_id: ${CONSUMER_GROUP_FISCAL_DOCUMENT_EVENTS}
    enabled: ${FISCAL_DOCUMENT_EVENTS_CONSUMER:false}
    poll_records: ${FISCAL_DOCUMENT_EVENTS_POLL_RECORDS:100}
  fiscal_documents_ens:
    group_id: ${CONSUMER_GROUP_FISCAL_DOCUMENTS_ENS}
    enabled: ${FISCAL_DOCUMENTS_ENS_CONSUMER:false}
  dual_writes:
    group_id: ${CONSUMER_GROUP_DUAL_WRITES}
    enabled: ${FISCAL_DOCUMENTS_DUAL_WRITES_CONSUMER:false}
    poll_records: ${FISCAL_DOCUMENT_DUAL_WRITES_POLL_RECORDS:100}
  default:
    backoff:
      ms: 300

scripts:
  credit_note:
    enabled: ${CREDIT_NOTE_SCRIPT_ENABLED:false}
    group_id: ${CONSUMER_GROUP_CREDIT_NOTE_SCRIPT}
ens:
  url: ${ENS_URL}

kafka:
  producers:
    enabled: ${KAFKA_PRODUCER_ENABLED:false}

sync:
  documents:
    interval-ms: 100
    initial-delay-ms: 1000

mfd:
  api:
    user: ${MFD_CB_API_USER}
    password: ${MFD_CB_API_PASSWORD}

db:
  writer:
    url: "jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    hikari:
      data-source-properties:
        reWriteBatchedInserts: true
        socketTimeout: 30000 # 30 seconds
      schema: ${DB_SCHEME:public}
      minimum-idle: ${HIKARI_MINIMUM_IDLE:4}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      keepalive-time: 60000
  reader:
    url: "jdbc:postgresql://${DB_HOST_READER}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    hikari:
      data-source-properties:
        socketTimeout: 30000 # 30 seconds
      schema: ${DB_SCHEME:public}
      minimum-idle: ${HIKARI_MINIMUM_IDLE:4}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      keepalive-time: 60000
