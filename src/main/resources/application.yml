debug: false

server:
  port: ${APP_PORT:8700}
  error:
    whitelabel:
      enabled: false
  shutdown: graceful


spring:
  application:
    name: fiscal-document-service
  main:
    banner-mode: off

  liquibase:
    enabled: ${LIQUIBASE_ENABLED:false}
    change-log: classpath:db/changelog/databaseChangeLog.xml
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?stringtype=unspecified
    password: ${DB_PASSWORD}
    user: ${DB_USER}
    default-schema: ${DB_SCHEME:public}

  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?stringtype=unspecified
    password: ${DB_PASSWORD}
    username: ${DB_USER}
    hikari:
      minimum-idle: ${HIKARI_MINIMUM_IDLE:4}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      data-source-properties:
        socketTimeout: 30000 # 30 seconds
      schema: ${DB_SCHEME:public}

  jpa:
    show-sql: ${SHOW_SQL:false}
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    open-in-view: false
    hibernate:
      ddl-auto: validate
    properties:
      hibernate:
        cache:
          use_second_level_cache: true
        order_by:
          default_null_ordering: last
        format_sql: false
        generate_statistics: false
        jdbc.batch_size: 50
        order_inserts: true
        order_updates: true
        hbm2ddl:
          extra_physical_table_types: PARTITIONED TABLE
      ## Enable @Cacheable annotation
      jakarta.persistence.sharedCache.mode: ENABLE_SELECTIVE

  kafka:
    bootstrap-servers: ${KAFKA_BROKER}
    consumer:
      key-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      properties:
        specific.avro.reader: true
        auto.offset.reset: earliest
        max.poll.interval.ms: ${KAFKA_MAX_POLL_INTERVAL:6000000}
    producer:
      key-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      properties:
        avro.remove.java.properties: true
        acks: all
        specific.avro.writer: true
    properties:
      schema.registry.url: ${SCHEMA_REGISTRY_HOST}
  web:
    resources:
      static-locations: classpath:/api_spec
  lifecycle:
    timeout-per-shutdown-phase: 60s
  data:
    redis:
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}

cloudbeds-shared:
  default-authentication-config:
    enabled: false
  authentication-filter:
    enabled: true
  security-expressions:
    enabled: true

db:
  writer:
    url: "jdbc:postgresql://${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}"
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
    hikari:
      data-source-properties:
        reWriteBatchedInserts: true
      schema: ${DATABASE_SCHEMA}
      maximum-pool-size: ${DATABASE_MAX_POOL_SIZE:10}
      keepalive-time: 60000
  reader:
    url: "jdbc:postgresql://${DATABASE_HOST_READER}:${DATABASE_PORT}/${DATABASE_NAME}"
    username: ${DATABASE_USERNAME}
    password: ${DATABASE_PASSWORD}
    hikari:
      schema: ${DATABASE_SCHEMA}
      maximum-pool-size: ${DATABASE_MAX_POOL_SIZE:10}
      keepalive-time: 60000

server:
  port: ${APP_PORT}
  shutdown: GRACEFUL

management:
  health:
    defaults:
      enabled: false
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: health, metrics, prometheus, shutdown
  endpoint:
    health:
      probes:
        enabled: true
      show-details: ALWAYS
    shutdown:
      enabled: true
  server:
    port: ${ACTUATOR_PORT}
  metrics:
    data:
      repository:
        autotime:
          enabled: false
    export:
      statsd:
        step: 10s
    tags:
      application: ${spring.application.name}
      service: ${spring.application.name}
      env: ${DD_ENV}
      tier: ${DD_TIER}
  statsd:
    metrics:
      export:
        flavor: datadog
        host: ${DD_AGENT_HOST}
        enabled: true
  datadog:
    metrics:
      export:
        step: 10s
        enabled: true
grpc:
  server:
    port: ${GRPC_PORT}
    shutdown-grace-period: 10s
  services:
    user:
      host: ${USER_SERVICE_HOST:user-service.cloudbeds-stage.com}
      port: ${USER_SERVICE_PORT:443}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS:5000}
    payment-config-service:
      host: ${PAYMENTS_CONFIG_SERVICE_HOST:payments-configuration-service.us1.cloudbeds-stage.com}
      port: ${PAYMENTS_CONFIG_SERVICE_PORT:443}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS:5000}
    payments-service:
      host: ${PAYMENTS_SERVICE_HOST:payments-processing-service.us1.cloudbeds-stage.com}
      port: ${PAYMENTS_SERVICE_PORT:443}
      shutdownTimeoutMs: ${GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS:5000}
    organization:
      host: ${ORGANIZATION_SERVICE_HOST:organization-service.cloudbeds-stage.com}
      port: ${ORGANIZATION_SERVICE_PORT:443}
      shutdownTimeoutMs: ${ORGANIZATION_CLIENT_SHUTDOWN_TIMEOUT_MS:5000}

config:
  island: ${ISLAND}

topics:
  transaction_codes: service.accounting.internal_transaction_codes
  mfd_fees_by_property: ${config.island}.service.accounting.mfd_fees_by_property
  mfd_taxes_by_property: ${config.island}.service.accounting.mfd_taxes_by_property
  transactions: ${config.island}.service.accounting.transactions
  trial_balance: ${config.island}.service.accounting.trial_balance
  accounts_receivable: ${config.island}.service.accounting.accounts_receivable_ledgers
  transaction_custom_codes: ${config.island}.service.accounting.transaction_custom_codes
  mfd_transaction_custom_codes: ${config.island}.service.accounting.mfd_transaction_custom_codes
  pending_transactions: ${config.island}.service.accounting.pending_transactions
  transaction_events: ${config.island}.service.accounting.transaction_events
  accounting_events: ${config.island}.service.accounting.events
  booking_rooms: ${config.island}.cdc.mfd.acessa.a_htl_booking_rooms
  addons: ${config.island}.cdc.mfd.acessa.a_htl_addons
  mfd_transactions: ${config.island}.cdc.mfd.acessa.a_htl_financial_transactions
  folios: ${config.island}.cdc.mfd.acessa.a_htl_folios
  folio_transactions: ${config.island}.cdc.mfd.acessa.a_htl_folios_transactions
  mfd_custom_items: ${config.island}.cdc.mfd.acessa.a_htl_custom_items
  mfd_inventory_products: ${config.island}.cdc.mfd.acessa.a_htl_inventory_products
  mfd_fees: ${config.island}.cdc.mfd.acessa.a_htl_fees
  mfd_taxes: ${config.island}.cdc.mfd.acessa.a_htl_taxes
  mfd_property: ${config.island}.cdc.mfd.acessa.a_htl_property
  bookings: ${config.island}.cdc.mfd.acessa.a_htl_bookings
  groups: ${config.island}.cdc.mfd.acessa.a_htl_group_profiles
  reservation_events: ${config.island}.service.mfd.events.reservations
  reservation_night_audit_events: ${config.island}.service.mfd.events.reservations.night_audit
  group_profile_events: ${config.island}.service.mfd.events.group_profile
  house_account_events: ${config.island}.service.mfd.events.house_account
  invalid_domain_events: ${config.island}.service.accounting.invalid_domain_events
  invalid_night_audit_events: ${config.island}.service.accounting.invalid_night_audit_events
  invalid_transaction_events: ${config.island}.service.accounting.invalid_transaction_events
  payment_events: ${config.island}.service.mfd.events.payments
  property_details: ${config.island}.dataset.accounting.property_details
  folio_transactions_dataset: ${config.island}.dataset.accounting.transactions_to_folio
  night_audit_processing_successful_events: ${config.island}.service.accounting.night_audit_processing_successful_events

transactioncodes:
  maxDelay: 60
  syncEnabled: true

consumer.backoff.ms: 300

bookings:
  pool:
    size: 10
  deleted_before_sec: ${DELETED_BOOKING_ROOMS_CONSUMER_END_TIME_SEC:0}

cleanup:
  pending-transactions:
    enabled: ${PENDING_TRANSACTIONS_CLEANUP_ENABLED:true}
    cron: ${PENDING_TRANSACTIONS_CLEANUP_CRON:0 0 0 * * *}
    retention-days: ${PENDING_TRANSACTIONS_CLEANUP_RETENTION_DAYS:180}
    property-page-size: ${PENDING_TRANSACTIONS_CLEANUP_PAGE_SIZE:100}
    transactions-page-size: ${PENDING_TRANSACTIONS_PAGE_SIZE:1000}

sync:
  transactions:
    interval-ms: 100
    initial-delay-ms: 1000
  accounts-receivable:
    interval-ms: 100
    initial-delay-ms: 1000
  night-audit-completed:
    interval-ms: 10000
    initial-delay-ms: 10000

parallelism:
  mfd-transaction-events: 20
  domain-events: 10

delayed_route_events:
  interval-ms: 10000
  initial-delay-ms: 1000

transaction_reporting_projections:
  cleanup:
    interval-ms: 3600000
    initial-delay-ms: 300000

app:
  use-db-datetime: ${USE_DB_DATETIME:true}

consumer:
  groups:
    mfd:
      fees: ${KAFKA_MFD_FEES_CONSUMER_GROUP_ID}
      properties: ${KAFKA_MFD_PROPERTIES_CONSUMER_GROUP_ID}
      addons: ${KAFKA_MFD_ADDONS_CONSUMER_GROUP_ID}
      bookings: ${KAFKA_MFD_BOOKINGS_CONSUMER_GROUP_ID}
      bookings_v2: ${KAFKA_MFD_BOOKINGS_V2_CONSUMER_GROUP_ID}
      groups: ${KAFKA_MFD_GROUPS_CONSUMER_GROUP_ID}
      folio-transactions: ${KAFKA_MFD_FOLIO_TRANSACTIONS_CONSUMER_GROUP_ID}
      reservation-events: ${KAFKA_MFD_RESERVATION_EVENTS_CONSUMER_GROUP_ID}
      reservation-night-audit-events: ${KAFKA_MFD_RESERVATION_NIGHT_AUDIT_EVENTS_CONSUMER_GROUP_ID}
      group-profile-events: ${KAFKA_MFD_GROUP_PROFILE_EVENTS_CONSUMER_GROUP_ID}
      house-account-events: ${KAFKA_MFD_HOUSE_ACCOUNT_EVENTS_CONSUMER_GROUP_ID}
      taxes: ${KAFKA_MFD_TAXES_CONSUMER_GROUP_ID}
      folios: ${KAFKA_MFD_FOLIOS_CONSUMER_GROUP_ID}
      booking-rooms: ${KAFKA_MFD_BOOKING_ROOMS_CONSUMER_GROUP_ID}
      booking-rooms-deleted: ${KAFKA_MFD_BOOKING_ROOMS_DELETED_CONSUMER_GROUP_ID}
      transactions: ${KAFKA_MFD_TRANSACTIONS_CONSUMER_GROUP_ID}
      custom-items: ${KAFKA_MFD_CUSTOM_ITEMS_CONSUMER_GROUP_ID}
      inventory-products: ${KAFKA_MFD_INVENTORY_PRODUCTS_CONSUMER_GROUP_ID}
      payment-events: ${KAFKA_MFD_PAYMENT_EVENTS_CONSUMER_GROUP_ID}
      payment-missing-voids: ${KAFKA_PAYMENT_MISSING_VOIDS_CONSUMER_GROUP_ID}
      folio-transactions-reprocessing: ${KAFKA_MFD_FOLIO_TRANSACTIONS_REPROCESSING_CONSUMER_GROUP_ID}
      transactions-reprocessing: ${KAFKA_MFD_TRANSACTIONS_REPROCESSING_CONSUMER_GROUP_ID}
    transaction-post-processing: ${KAFKA_TRANSACTION_CUSTOM_CODES_CONSUMER_GROUP_ID}
    send-transactions-ens-events: ${KAFKA_TRANSACTION_SEND_ENS_EVENTS_CONSUMER_GROUP_ID}
    transaction-check-duplicate-payments: ${KAFKA_TRANSACTION_CHECK_DUPLICATE_PAYMENTS_GROUP_ID}
    pending-transaction-post-processing: ${KAFKA_PENDING_TRANSACTION_CUSTOM_CODES_CONSUMER_GROUP_ID}
    transaction-events: ${KAFKA_TRANSACTION_EVENTS_CONSUMER_GROUP_ID}
    accounting-events: ${KAFKA_ACCOUNTING_EVENTS_CONSUMER_GROUP_ID}
    successful-accounting-events: ${KAFKA_SUCCESSFUL_ACCOUNTING_EVENTS_CONSUMER_GROUP_ID}
    invalid-domain-events: ${KAFKA_INVALID_DOMAIN_EVENTS_CONSUMER_GROUP_ID}
    invalid-night-audit-events: ${KAFKA_INVALID_NIGHT_AUDIT_EVENTS_CONSUMER_GROUP_ID}
    invalid-transaction-events: ${KAFKA_INVALID_TRANSACTION_EVENTS_CONSUMER_GROUP_ID}
    property-details: ${KAFKA_PROPERTY_DETAILS_CONSUMER_GROUP_ID}
    folio-transactions-dataset: ${KAFKA_FOLIO_TRANSACTIONS_CONSUMER_GROUP_ID}
    night_audit_processing_successful_events: ${KAFKA_NIGHT_AUDIT_PROCESSING_SUCCESSFUL_EVENTS_CONSUMER_GROUP_ID}

  poll_records:
    transaction_item_mapping: ${CONSUMER_GROUP_POLL_RECORDS_TRANSACTION_ITEM_MAPPING:100}
    transaction_events: ${CONSUMER_GROUP_POLL_RECORDS_TRANSACTION_EVENTS:300}
    mfd_transactions: ${CONSUMER_GROUP_POLL_RECORDS_MFD_TRANSACTIONS:300}
    reservation_events: ${CONSUMER_GROUP_POLL_RECORDS_RESERVATION_EVENTS:300}
    reservation_night_audit_events: ${CONSUMER_GROUP_POLL_RECORDS_RESERVATION_NIGHT_AUDIT_EVENTS:100}
    group_profile_events: ${CONSUMER_GROUP_POLL_RECORDS_GROUP_PROFILE_EVENTS:300}
    house_account_events: ${CONSUMER_GROUP_POLL_RECORDS_HOUSE_ACCOUNT_EVENTS:300}
    invalid_domain_events: ${CONSUMER_GROUP_POLL_RECORDS_INVALID_DOMAIN_EVENTS:300}
    invalid_night_audit_events: ${CONSUMER_GROUP_POLL_RECORDS_INVALID_NIGHT_AUDIT_EVENTS:300}
    payment_events: ${CONSUMER_GROUP_POLL_RECORDS_PAYMENT_EVENTS:300}
    property_details: ${CONSUMER_GROUP_POLL_RECORDS_PROPERTY_DETAILS:300}
    folio_transactions: ${CONSUMER_GROUP_POLL_RECORDS_FOLIO_TRANSACTIONS:500}
    night_audit_processing_successful: ${CONSUMER_GROUP_POLL_RECORDS_NIGHT_AUDIT_PROCESSING_SUCCESSFUL:50}
    post_processing_transactions: ${CONSUMER_GROUP_POLL_RECORDS_POST_PROCESSING_TRANSACTIONS:300}
    post_processing_pending_transactions: ${CONSUMER_GROUP_POLL_RECORDS_POST_PROCESSING_PENDING_TRANSACTIONS:300}
    accounting_events: ${CONSUMER_GROUP_POLL_RECORDS_ACCOUNTING_EVENTS:300}
    send_accounting_ens_transactions_listener: ${CONSUMER_SEND_ACCOUNTING_ENS_EVENTS_TRANSACTIONS:300}
  concurrency:
    transaction_events: ${CONSUMER_GROUP_CONCURRENCY_TRANSACTION_EVENTS:6}
  acknowledgement_offset:
    invalid_domain_events: ${ACKNOWLEDGEMENT_OFFSET_INVALID_DOMAIN_EVENTS:-1}
    invalid_transaction_events: ${ACKNOWLEDGEMENT_OFFSET_INVALID_TRANSACTION_EVENTS:-1}

  retry_offset:
    invalid_transaction_events: ${RETRY_OFFSET_INVALID_TRANSACTION_EVENTS:-1}

cloudbeds:
  mappings-service-url: ${MAPPING_SERVICE_URL:https://api.cloudbeds-stage.com/mapping/}

distributedid:
  enabled: true
  pool-size: ${DISTRIBUTED_ID_POOL_SIZE:100000}
  threshold-percent: 0.7
  server-host: ${DISTRIBUTED_ID_SERVICE_HOST:distributed-id-service.cloudbeds-stage.com}
  server-port: ${DISTRIBUTED_ID_SERVICE_PORT:443}
  service-id: ${spring.application.name}
  channel-type: ${DISTRIBUTED_ID_CHANNEL_TYPE:INSECURE}
  lazy-init: true

listeners:
  # enable taxes and fees items listeners
  enabled: true
  # enable mfd transactions and folios listeners that produce transaction events
  mfd_transaction_events: ${MFD_TRANSACTION_EVENTS_CONSUMER:false}
  # enable transaction events processing
  transaction_events_processing: ${TRANSACTION_EVENTS_CONSUMER:false}
  domain_events: ${DOMAIN_EVENTS_CONSUMER:false}
  payment_domain_events: ${PAYMENT_DOMAIN_EVENTS_CONSUMER:false}
  invalid_domain_events: ${INVALID_DOMAIN_EVENTS_CONSUMER:false}
  invalid_night_audit_events: ${INVALID_NIGHT_AUDIT_EVENTS_CONSUMER:false}
  invalid_transaction_events: ${INVALID_TRANSACTION_EVENTS_CONSUMER:false}
  mfd_transaction_events_reprocessing: ${MFD_TRANSACTION_EVENT_REPROCESSING_CONSUMER:false}
  successful_accounting_events: true
  deleted_booking_rooms: ${DELETED_BOOKING_ROOMS_CONSUMER:false}

dynamic-query:
  default-page-size: 100
  min-page-size: 1
  max-page-size: 1100

mfd_public_transactions_api:
  count_cache.enabled: ${MFD_PUBLIC_TRANSACTIONS_API_COUNT_CACHE_ENABLED:true}

# ===============================
# = Spring Doc - Swagger
# ===============================
springdoc:
  swagger-ui:
    enabled: true
    url: /accounting/accounting-openapi.yaml
    path: /accounting/swagger-ui
  api-docs:
    path: /accounting/v3/api-docs
    groups:
      enable: ${SWAGGER_UI:false}

kafka:
  producers:
    disabled: true

delete_reservations_script:
  enabled: ${DELETE_RESERVATION_SCRIPT_ENABLED:false}
  limit: ${DELETE_RESERVATION_SCRIPT_LIMIT:100}
  property_id: ${DELETE_RESERVATION_SCRIPT_PROPERTY_ID:****************}
  source_file: ${DELETE_RESERVATION_SCRIPT_SOURCE_FILE:src/main/resources/scripts/****************_deleted_reservations.csv}

reprocess_script:
  enabled: ${REPROCESS_SCRIPT_ENABLED:false}
  skip_events: ${REPROCESS_SCRIPT_SKIP_EVENTS:false}
  limit: ${REPROCESS_SCRIPT_LIMIT:1}
  property_ids: ${REPROCESS_SCRIPT_PROPERTY_IDS:}

transaction-check-duplicate-payments:
  enabled: ${PAYMENT_TRANSACTIONS_CHECK:false}

update_transaction_script:
  enabled: ${UPDATE_TRANSACTION_SCRIPT_ENABLED:false}
  posted_transaction_ids_per_property: ${UPDATE_TRANSACTION_SCRIPT_POSTED_TRANSACTION_IDS_PER_PROPERTY:}
  pending_transaction_ids_per_property: ${UPDATE_TRANSACTION_SCRIPT_PENDING_TRANSACTION_IDS_PER_PROPERTY:}
  pending_transaction_ids_for_remove: ${UPDATE_TRANSACTION_SCRIPT_PENDING_TRANSACTION_IDS_FOR_REMOVE:}
  posted_transaction_ids_for_remove: ${UPDATE_TRANSACTION_SCRIPT_POSTED_TRANSACTION_IDS_FOR_REMOVE:}

void_transaction_script:
  enabled: ${VOID_TRANSACTION_SCRIPT_ENABLED:false}
  posted_transaction_ids_per_property: ${VOID_TRANSACTION_SCRIPT_POSTED_TRANSACTION_IDS_PER_PROPERTY:}
  pending_transaction_ids_per_property: ${VOID_TRANSACTION_SCRIPT_PENDING_TRANSACTION_IDS_PER_PROPERTY:}

update_transaction_description_script:
  enabled: ${UPDATE_TRANSACTION_DESCRIPTION_FIX_SCRIPT_ENABLED:false}
  property_id: ${UPDATE_TRANSACTION_DESCRIPTION_FIX_SCRIPT_PROPERTY_ID:}
  source_id: ${UPDATE_TRANSACTION_DESCRIPTION_FIX_SCRIPT_SOURCE_ID:}
  source_kind: ${UPDATE_TRANSACTION_DESCRIPTION_FIX_SCRIPT_SOURCE_KIND:}
  page_size: ${UPDATE_TRANSACTION_DESCRIPTION_FIX_SCRIPT_PAGE_SIZE:100}

collect_missing_voids_script:
  enabled: ${COLLECT_MISSING_VOIDS_FIX_SCRIPT:false}
  just_collect: ${COLLECT_MISSING_VOIDS_DO_NOT_SEND_EVENTS:true}
  payment_ids: ${COLLECT_MISSING_VOIDS_PAYMENT_IDS:}

domain-events:
  max-nights-batch: 500
  batch-size-delete-event: 500

# https://resilience4j.readme.io/docs/getting-started-3
resilience4j:
  circuitbreaker:
    configs:
      default:
        registerHealthIndicator: false
        automaticTransitionFromOpenToHalfOpenEnabled: true
      shared:
        sliding-window-type: TIME_BASED
        slidingWindowSize: 20
        minimumNumberOfCalls: 50
        permittedNumberOfCallsInHalfOpenState: 10
        waitDurationInOpenState: 3s
        failureRateThreshold: 50
    instances:
      paymentMethodsGRPC:
        baseConfig: shared
        slowCallRateThreshold: ${BEAKER_PAYMENT_METHOD_DETAILS_RATE_THRESHOLD_PERCENT:50} # percent
        slowCallDurationThreshold: ${BEAKER_PAYMENT_METHOD_DETAILS_DURATION_THRESHOLD_MS:200} # ms
  retry:
    instances:
      paymentsGetPayment:
        enable-exponential-backoff: true
        exponentialBackoffMultiplier: 2
        maxAttempts: ${RETRY_PAYMENTS_GET_PAYMENT_MAX_ATTEMPTS:3} # default total of about 1 min
        waitDuration: 1s

      getBookingRoomWithRetry:
        enable-exponential-backoff: true
        exponentialBackoffMultiplier: 2
        maxAttempts: ${RETRY_PAYMENTS_GET_BOOKING_ROOM:7} # default total of about 1 min
        waitDuration: 1s
        retryExceptions:
          - com.cloudbeds.accounting.support.exception.AccountingException

      findArTransactionByAccountingEvent:
        enable-exponential-backoff: false
        maxAttempts: 3
        waitDuration: 1s
        retryExceptions:
          - com.cloudbeds.accounting.support.exception.AccountingException

      orgServiceListProperties:
        enable-exponential-backoff: false
        maxAttempts: 3
        waitDuration: 5s

      getAllProperties:
        enable-exponential-backoff: false
        maxAttempts: 5
        waitDuration: 1s
        retryExceptions:
          - io.grpc.StatusRuntimeException

db:
  writer:
    url: "jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    hikari:
      data-source-properties:
        reWriteBatchedInserts: true
      schema: ${DB_SCHEME:public}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      keepalive-time: 60000
  reader:
    url: "jdbc:postgresql://${DB_HOST_READER}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    hikari:
      schema: ${DB_SCHEME:public}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      keepalive-time: 60000

      sendAccountingEvents:
        enable-exponential-backoff: false
        maxAttempts: 5
        waitDuration: 5s
        retryExceptions:
          - java.lang.RuntimeException

  bulkhead:
    instances:
      listMfdPublicApiTransactions:
        maxWaitDuration: 10s
        maxConcurrentCalls: 5

mfd:
  api:
    user: ${MFD_CB_API_USER}
    password: ${MFD_CB_API_PASSWORD}

ens:
  endpoint_url: ${ENS_ENDPOINT_URL}
  event_chunk_size: ${ENS_EVENT_CHUNK_SIZE:25}
  listeners:
    accounting:
      enabled: ${ENS_ENABLED:false}

db:
  writer:
    url: "jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    hikari:
      data-source-properties:
        reWriteBatchedInserts: true
      schema: ${DB_SCHEME:public}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      keepalive-time: 60000
  reader:
    url: "jdbc:postgresql://${DB_HOST_READER}:${DB_PORT}/${DB_NAME}"
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    hikari:
      schema: ${DB_SCHEME:public}
      maximum-pool-size: ${HIKARI_MAXIMUM_POOL_SIZE:64}
      keepalive-time: 60000