package com.cloudbeds.fiscaldocument.support.database.aspect;

import com.cloudbeds.fiscaldocument.support.database.routing.DbContextHolder;
import com.cloudbeds.fiscaldocument.support.database.routing.DbType;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Aspect
@Component
@Slf4j
public class ReadOnlyConnectionInterceptor implements Ordered {
    /**
     * Add DB type to context based on read only flag of transactional.
     *
     * @param pjp ProceedingJoinPoint
     * @param transactional transactional
     * @return Object
     * @throws Throwable Throwable
     */
    @Around(value = "@annotation(transactional)")
    public Object proceed(ProceedingJoinPoint pjp, Transactional transactional) throws Throwable {
        try {
            if (transactional.readOnly()) {
                DbContextHolder.setDbType(DbType.READ);
            } else {
                DbContextHolder.setDbType(DbType.WRITE);
            }
            Object result = pjp.proceed();
            DbContextHolder.clearDbType();
            return result;
        } finally {
            DbContextHolder.clearDbType();
        }
    }

    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE + 1;
    }
}
