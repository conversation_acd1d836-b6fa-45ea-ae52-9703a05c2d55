package com.cloudbeds.fiscaldocument.support.database;

import com.cloudbeds.fiscaldocument.support.database.routing.ClientDataSourceRouter;
import com.cloudbeds.fiscaldocument.support.database.routing.DbType;
import com.zaxxer.hikari.HikariDataSource;
import java.util.HashMap;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

@Configuration
@ConditionalOnProperty(name = "db.reader.url")
@Profile("!test")
public class DatabaseConfig {

    /**
     * Creates DataSource properties for the writer database.
     *
     * @return DataSourceProperties configured for writer database
     */
    @Bean("writerDataSourceProperties")
    @ConfigurationProperties(prefix = "db.writer")
    public DataSourceProperties writerDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * Creates DataSource properties for the reader database.
     *
     * @return DataSourceProperties configured for reader database
     */
    @Bean("readerDataSourceProperties")
    @ConfigurationProperties(prefix = "db.reader")
    public DataSourceProperties readerDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * Creates the writer DataSource using HikariCP.
     *
     * @param properties the DataSource properties for writer database
     * @return DataSource configured for write operations
     */
    @Bean("writerDataSource")
    @ConfigurationProperties("db.writer.hikari")
    public DataSource writerDataSource(@Qualifier("writerDataSourceProperties") DataSourceProperties properties) {
        return properties
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * Creates the reader DataSource using HikariCP.
     *
     * @param properties the DataSource properties for reader database
     * @return DataSource configured for read operations
     */
    @Bean("readerDataSource")
    @ConfigurationProperties("db.reader.hikari")
    public DataSource readerDataSource(@Qualifier("readerDataSourceProperties") DataSourceProperties properties) {
        return properties
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * Creates the main routing DataSource that routes queries to reader or writer databases
     * based on transaction type.
     *
     * @param writerDataSource the DataSource for write operations
     * @param readerDataSource the DataSource for read operations
     * @return DataSource that routes queries based on transaction context
     */
    @Bean("dataSource")
    @Primary
    public DataSource routingDataSource(@Qualifier("writerDataSource") DataSource writerDataSource,
                                       @Qualifier("readerDataSource") DataSource readerDataSource) {
        var targetDataSources = new HashMap<Object, Object>();
        targetDataSources.put(DbType.WRITE, writerDataSource);
        targetDataSources.put(DbType.READ, readerDataSource);

        var clientRoutingDatasource = new ClientDataSourceRouter();
        clientRoutingDatasource.setTargetDataSources(targetDataSources);
        clientRoutingDatasource.setDefaultTargetDataSource(writerDataSource);
        clientRoutingDatasource.afterPropertiesSet();
        return clientRoutingDatasource;
    }
}
