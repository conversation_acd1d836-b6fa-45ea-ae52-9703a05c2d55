package com.cloudbeds.fiscaldocument.support.database;

import com.cloudbeds.fiscaldocument.support.database.routing.ClientDataSourceRouter;
import com.cloudbeds.fiscaldocument.support.database.routing.DbType;
import com.zaxxer.hikari.HikariDataSource;
import java.util.HashMap;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@ConditionalOnProperty(name = "app.database.reader-writer.enabled", havingValue = "true")
public class DatabaseConfig {

    @Bean("writerDataSourceProperties")
    @ConfigurationProperties(prefix = "db.writer")
    public DataSourceProperties writerDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean("readerDataSourceProperties")
    @ConfigurationProperties(prefix = "db.reader")
    public DataSourceProperties readerDataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean("writerDataSource")
    @ConfigurationProperties("db.writer.hikari")
    public DataSource writerDataSource(@Qualifier("writerDataSourceProperties") DataSourceProperties properties) {
        return properties
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    @Bean("readerDataSource")
    @ConfigurationProperties("db.reader.hikari")
    public DataSource readerDataSource(@Qualifier("readerDataSourceProperties") DataSourceProperties properties) {
        return properties
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    @Bean("dataSource")
    @Primary
    public DataSource routingDataSource(@Qualifier("writerDataSource") DataSource writerDataSource,
                                       @Qualifier("readerDataSource") DataSource readerDataSource) {
        var targetDataSources = new HashMap<Object, Object>();
        targetDataSources.put(DbType.WRITE, writerDataSource);
        targetDataSources.put(DbType.READ, readerDataSource);

        var clientRoutingDatasource = new ClientDataSourceRouter();
        clientRoutingDatasource.setTargetDataSources(targetDataSources);
        clientRoutingDatasource.setDefaultTargetDataSource(writerDataSource);
        clientRoutingDatasource.afterPropertiesSet();
        return clientRoutingDatasource;
    }
}
