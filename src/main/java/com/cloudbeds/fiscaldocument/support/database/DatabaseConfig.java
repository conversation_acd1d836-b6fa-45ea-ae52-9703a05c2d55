package com.cloudbeds.fiscaldocument.support.database;

import com.cloudbeds.fiscaldocument.support.database.routing.ClientDataSourceRouter;
import com.cloudbeds.fiscaldocument.support.database.routing.DbType;
import java.util.HashMap;
import javax.sql.DataSource;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class DatabaseConfig {

    @Bean
    @ConfigurationProperties("db.writer.hikari")
    public DataSource readWriteConfiguration() {
        return writeDataSourceProperties().initializeDataSourceBuilder().build();
    }

    @Bean
    @ConfigurationProperties(prefix = "db.writer")
    public DataSourceProperties writeDataSourceProperties() {
        return new DataSourceProperties();
    }


    @Bean
    @ConfigurationProperties("db.reader.hikari")
    public DataSource readOnlyConfiguration() {
        return readDataSourceProperties().initializeDataSourceBuilder().build();
    }

    @Bean
    @ConfigurationProperties(prefix = "db.reader")
    public DataSourceProperties readDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * Create main datasource with routing based on Db type.
     *
     * @return data source
     */
    @Bean("dataSource")
    @Primary
    public DataSource dataSource() {
        var targetDataSources = new HashMap<>();
        targetDataSources.put(DbType.WRITE, readWriteConfiguration());
        targetDataSources.put(DbType.READ, readOnlyConfiguration());

        var clientRoutingDatasource = new ClientDataSourceRouter();
        clientRoutingDatasource.setTargetDataSources(targetDataSources);
        clientRoutingDatasource.setDefaultTargetDataSource(readWriteConfiguration());
        return clientRoutingDatasource;
    }
}
