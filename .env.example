ENV=local
COMPOSE_PROJECT_NAME=fiscal-document-service
ISLAND=us2

DB_ENGINE=postgresql
DB_HOST=localhost
DB_HOST_READER=localhost
DB_NAME=fiscaldocument
DB_SCHEME=public
DB_PASSWORD=pass
DB_PORT=5433
DB_URL=***********************************************
DB_USER=user

JPA_SHOW_SQL=true

LIQUIBASE_ENABLED=true

REDIS_HOST=localhost
REDIS_PORT=6380

APP_PORT=8700
GRPC_PORT=9190
LOG_LEVEL=INFO

DATADOG_METRICS_ENABLED=false
DATADOG_API_KEY=not_null
DATADOG_APPLICATION_KEY=
DATADOG_HOST_KEY=

KAFKA_SERVER=$KAFKA_SERVER
KAFKA_MAX_POLL_RECORDS=1000
KAFKA_IDLE_BETWEEN_POLLS=5

CONFLUENT_API_KEY=$CONFLUENT_API_KEY
CONFLUENT_API_SECRET=$CONFLUENT_API_SECRET
CONFLUENT_SCHEMA_REGISTRY_URL=$CONFLUENT_SCHEMA_REGISTRY_URL
CONFLUENT_SCHEMA_KEY=$CONFLUENT_SCHEMA_KEY
CONFLUENT_SCHEMA_SECRET=$CONFLUENT_SCHEMA_SECRET

DISTRIBUTED_ID_SERVICE_HOST=$DISTRIBUTED_ID_SERVICE_HOST
DISTRIBUTED_ID_SERVICE_PORT=$DISTRIBUTED_ID_SERVICE_PORT
DISTRIBUTED_ID_CHANNEL_TYPE=$DISTRIBUTED_ID_CHANNEL_TYPE

CONSUMER_GROUP_INVOICE_SETUP=$CONSUMER_GROUP_INVOICE_SETUP
CONSUMER_GROUP_FISCAL_DOCUMENT_EVENTS=$CONSUMER_GROUP_FISCAL_DOCUMENT_EVENTS
CONSUMER_GROUP_INVOICES=$CONSUMER_GROUP_INVOICES
CONSUMER_GROUP_FISCAL_DOCUMENTS_ENS=$CONSUMER_GROUP_FISCAL_DOCUMENTS_ENS
CONSUMER_GROUP_DUAL_WRITES=$CONSUMER_GROUP_DUAL_WRITES

SWAGGER_UI=true

AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_ENDPOINT=http://kubernetes.docker.internal:4566

SENDGRID_API_KEY=$SENDGRID_API_KEY

MFD_CB_API_USER=$MFD_CB_API_USER
MFD_CB_API_PASSWORD=$MFD_CB_API_PASSWORD

ACCOUNTING_SERVICE_HOST=accounting-service.us2.cloudbeds-stage.com
ACCOUNTING_SERVICE_PORT=443

ORGANIZATION_SERVICE_HOST=organization-service.cloudbeds-stage.com
ORGANIZATION_SERVICE_PORT=443

MFD_GRPC_SERVICE_HOST=mfd-grpc.us2.cloudbeds-stage.com
MFD_GRPC_SERVICE_PORT=443

MFD_GRPC_SERVICE_HOST=mfd-grpc.us2.cloudbeds-stage.com
MFD_GRPC_SERVICE_PORT=443

GUEST_REQUIREMENT_SERVICE_HOST=guest-requirements-service.us2.cloudbeds-stage.com
GUEST_REQUIREMENT_SERVICE_PORT=443

GUEST_SERVICE_HOST=guest-service.us2.cloudbeds-stage.com
GUEST_SERVICE_PORT=443

MFD_GRPC_SERVICE_HOST=mfd-grpc.us2.cloudbeds-stage.com
MFD_GRPC_SERVICE_PORT=443
GRPC_CLIENT_SHUTDOWN_TIMEOUT_MS=5000

ENS_URL=http://100%realnofake.com